2025-07-21 17:19:20.734 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:19:20.734 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:21:07.795 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:21:07.796 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:21:48.171 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:21:48.171 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:24:13.957 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:24:13.957 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:25:36.196 | ERROR    | scripts.database:check_database_connection:94 - 数据库连接失败: object Row can't be used in 'await' expression
2025-07-21 17:25:36.199 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:26:53.639 | ERROR    | scripts.admin_data:init_admin_data:45 - 管理员系统数据初始化失败: (asyncmy.errors.DataError) (1406, "Data too long for column 'menu_type' at row 1")
[SQL: INSERT INTO admin_auth_menu (delete_datetime, is_delete, title, icon, path, name, component, redirect, disabled, hidden, `order`, menu_type, parent_id) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]
[parameters: (None, 0, '系统管理', 'system', '/system', 'System', 'Layout', None, 0, 0, 1, 'directory', None)]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-21 17:26:53.639 | ERROR    | scripts.init_data:init_all_data:40 - 管理员系统数据初始化失败，初始化中止
2025-07-21 17:30:17.323 | ERROR    | scripts.admin_data:init_admin_data:45 - 管理员系统数据初始化失败: 'perms' is an invalid keyword argument for AdminMenu
2025-07-21 17:30:17.324 | ERROR    | scripts.init_data:init_all_data:40 - 管理员系统数据初始化失败，初始化中止
2025-07-21 17:34:32.611 | ERROR    | scripts.admin_data:init_admin_data:45 - 管理员系统数据初始化失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT admin_auth_menu.id AS admin_auth_menu_id, admin_auth_menu.create_datetime AS admin_auth_menu_create_datetime, admin_auth_menu.update_datetime AS admin_auth_menu_update_datetime, admin_auth_menu.delete_datetime AS admin_auth_menu_delete_datetime, admin_auth_menu.is_delete AS admin_auth_menu_is_delete, admin_auth_menu.title AS admin_auth_menu_title, admin_auth_menu.icon AS admin_auth_menu_icon, admin_auth_menu.path AS admin_auth_menu_path, admin_auth_menu.name AS admin_auth_menu_name, admin_auth_menu.component AS admin_auth_menu_component, admin_auth_menu.redirect AS admin_auth_menu_redirect, admin_auth_menu.disabled AS admin_auth_menu_disabled, admin_auth_menu.hidden AS admin_auth_menu_hidden, admin_auth_menu.`order` AS admin_auth_menu_order, admin_auth_menu.menu_type AS admin_auth_menu_menu_type, admin_auth_menu.parent_id AS admin_auth_menu_parent_id 
FROM admin_auth_menu, admin_auth_role_menus 
WHERE %s = admin_auth_role_menus.role_id AND admin_auth_menu.id = admin_auth_role_menus.menu_id]
[parameters: [{'%(2459658676240 param)s': 1}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-21 17:34:32.611 | ERROR    | scripts.init_data:init_all_data:40 - 管理员系统数据初始化失败，初始化中止
