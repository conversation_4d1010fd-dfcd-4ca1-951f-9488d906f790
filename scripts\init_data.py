#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : init_data.py
# @IDE            : PyCharm
# @desc           : 数据初始化主入口

import asyncio
from core.logging import logger
from .database import init_database_tables, check_database_connection
from .admin_data import init_admin_data


async def init_all_data(env: str = "pro"):
    """
    初始化所有数据
    
    Args:
        env: 环境标识 (dev/pro)
    """
    try:
        logger.info(f"开始初始化 {env} 环境数据...")
        
        # 1. 检查数据库连接
        logger.info("检查数据库连接...")
        if not await check_database_connection():
            logger.error("数据库连接失败，初始化中止")
            return False
        
        # 2. 初始化数据库表结构
        logger.info("初始化数据库表结构...")
        if not await init_database_tables():
            logger.error("数据库表结构初始化失败，初始化中止")
            return False
        
        # 3. 初始化管理员系统数据
        logger.info("初始化管理员系统数据...")
        if not await init_admin_data():
            logger.error("管理员系统数据初始化失败，初始化中止")
            return False
        
        # 4. 初始化系统配置数据（如果需要）
        logger.info("初始化系统配置数据...")
        if not await init_system_data():
            logger.warning("系统配置数据初始化失败，但不影响整体初始化")
        
        logger.info(f"{env} 环境数据初始化完成！")
        logger.info("默认管理员账号: 13800138000")
        logger.info("默认管理员密码: admin123")
        
        return True
        
    except Exception as e:
        logger.error(f"数据初始化过程中发生错误: {str(e)}")
        return False


async def init_system_data():
    """
    初始化系统配置数据
    """
    try:
        from .system_data import init_system_data as _init_system_data
        return await _init_system_data()

    except Exception as e:
        logger.error(f"系统配置数据初始化失败: {str(e)}")
        return False


async def init_dict_data():
    """
    初始化字典数据
    """
    # TODO: 实现字典数据初始化
    pass


async def init_settings_data():
    """
    初始化系统设置数据
    """
    # TODO: 实现系统设置数据初始化
    pass


def run_init(env: str = "pro"):
    """
    同步方式运行初始化
    供命令行调用
    
    Args:
        env: 环境标识
    """
    return asyncio.run(init_all_data(env))


if __name__ == "__main__":
    # 直接运行此脚本进行完整初始化
    import sys
    env = sys.argv[1] if len(sys.argv) > 1 else "pro"
    success = run_init(env)
    if success:
        print("数据初始化成功！")
    else:
        print("数据初始化失败！")
        sys.exit(1)
