#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : admin_data.py
# @IDE            : PyCharm
# @desc           : 管理员系统初始数据

import asyncio
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from core.database import session_factory
from core.logging import logger
from config.setting import DEFAULT_PASSWORD, DEFAULT_AVATAR

# 导入模型
from models.admin.auth.user import AdminUser
from models.admin.auth.role import AdminRole
from models.admin.auth.dept import AdminDept
from models.admin.auth.menu import AdminMenu


async def init_admin_data():
    """
    初始化管理员系统数据
    """
    try:
        logger.info("开始初始化管理员系统数据...")
        
        async with session_factory() as session:
            # 按顺序初始化数据
            await init_departments(session)
            await init_menus(session)
            await init_roles(session)
            await init_users(session)
            
            # 提交事务
            await session.commit()
            
        logger.info("管理员系统数据初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"管理员系统数据初始化失败: {str(e)}")
        return False


async def init_departments(session: AsyncSession):
    """
    初始化部门数据
    """
    logger.info("初始化部门数据...")
    
    # 检查是否已存在部门数据
    result = await session.execute(select(AdminDept))
    if result.first():
        logger.info("部门数据已存在，跳过初始化")
        return
    
    # 创建根部门
    root_dept = AdminDept(
        name="总公司",
        dept_key="root",
        disabled=False,
        order=1,
        desc="根部门",
        owner="系统管理员",
        parent_id=None
    )
    session.add(root_dept)
    await session.flush()  # 获取ID
    
    # 创建子部门
    departments = [
        {
            "name": "技术部",
            "dept_key": "tech",
            "desc": "技术开发部门",
            "order": 1,
            "parent_id": root_dept.id
        },
        {
            "name": "产品部", 
            "dept_key": "product",
            "desc": "产品管理部门",
            "order": 2,
            "parent_id": root_dept.id
        },
        {
            "name": "运营部",
            "dept_key": "operation", 
            "desc": "运营管理部门",
            "order": 3,
            "parent_id": root_dept.id
        }
    ]
    
    for dept_data in departments:
        dept = AdminDept(**dept_data, disabled=False)
        session.add(dept)
    
    logger.info("部门数据初始化完成")


async def init_menus(session: AsyncSession):
    """
    初始化菜单数据
    """
    logger.info("初始化菜单数据...")
    
    # 检查是否已存在菜单数据
    result = await session.execute(select(AdminMenu))
    if result.first():
        logger.info("菜单数据已存在，跳过初始化")
        return
    
    # 创建主菜单
    main_menus = [
        {
            "title": "系统管理",
            "name": "System",
            "path": "/system",
            "component": "Layout",
            "menu_type": "directory",
            "icon": "system",
            "order": 1,
            "parent_id": None,
            "hidden": False,
            "disabled": False
        }
    ]
    
    for menu_data in main_menus:
        menu = AdminMenu(**menu_data)
        session.add(menu)
        await session.flush()
        
        # 创建子菜单
        if menu_data["name"] == "System":
            sub_menus = [
                {
                    "title": "用户管理",
                    "name": "User",
                    "path": "/system/user",
                    "component": "system/user/index",
                    "menu_type": "menu",
                    "icon": "user",
                    "order": 1,
                    "parent_id": menu.id,
                    "perms": "system:user:list"
                },
                {
                    "title": "角色管理", 
                    "name": "Role",
                    "path": "/system/role",
                    "component": "system/role/index",
                    "menu_type": "menu",
                    "icon": "role",
                    "order": 2,
                    "parent_id": menu.id,
                    "perms": "system:role:list"
                },
                {
                    "title": "部门管理",
                    "name": "Dept", 
                    "path": "/system/dept",
                    "component": "system/dept/index",
                    "menu_type": "menu",
                    "icon": "dept",
                    "order": 3,
                    "parent_id": menu.id,
                    "perms": "system:dept:list"
                },
                {
                    "title": "菜单管理",
                    "name": "Menu",
                    "path": "/system/menu", 
                    "component": "system/menu/index",
                    "menu_type": "menu",
                    "icon": "menu",
                    "order": 4,
                    "parent_id": menu.id,
                    "perms": "system:menu:list"
                }
            ]
            
            for sub_menu_data in sub_menus:
                sub_menu = AdminMenu(**sub_menu_data, hidden=False, disabled=False)
                session.add(sub_menu)
    
    logger.info("菜单数据初始化完成")


async def init_roles(session: AsyncSession):
    """
    初始化角色数据
    """
    logger.info("初始化角色数据...")

    # 检查是否已存在角色数据
    result = await session.execute(select(AdminRole))
    if result.first():
        logger.info("角色数据已存在，跳过初始化")
        return

    # 创建超级管理员角色
    admin_role = AdminRole(
        name="超级管理员",
        role_key="admin",
        desc="系统超级管理员，拥有所有权限",
        disabled=False,
        is_admin=True,
        order=1
    )
    session.add(admin_role)
    await session.flush()  # 获取ID

    # 创建普通角色
    user_role = AdminRole(
        name="普通用户",
        role_key="user",
        desc="普通用户角色",
        disabled=False,
        is_admin=False,
        order=2
    )
    session.add(user_role)
    await session.flush()  # 获取ID

    # 为超级管理员角色分配所有菜单权限
    menus = await session.execute(select(AdminMenu))
    all_menus = menus.scalars().all()

    for menu in all_menus:
        admin_role.menus.add(menu)

    logger.info("角色数据初始化完成")


async def init_users(session: AsyncSession):
    """
    初始化用户数据
    """
    logger.info("初始化用户数据...")
    
    # 检查是否已存在用户数据
    result = await session.execute(select(AdminUser))
    if result.first():
        logger.info("用户数据已存在，跳过初始化")
        return
    
    # 获取超级管理员角色和根部门
    admin_role = await session.execute(
        select(AdminRole).where(AdminRole.role_key == "admin")
    )
    admin_role = admin_role.scalar_one()
    
    root_dept = await session.execute(
        select(AdminDept).where(AdminDept.dept_key == "root")
    )
    root_dept = root_dept.scalar_one()
    
    # 创建超级管理员用户
    admin_user = AdminUser(
        name="超级管理员",
        nickname="admin",
        telephone="13800138000",
        email="<EMAIL>",
        password=AdminUser.get_password_hash("admin123"),
        avatar=DEFAULT_AVATAR,
        gender="1",
        is_active=True,
        is_staff=True,
        is_reset_password=False
    )
    
    # 建立关联关系
    admin_user.roles.add(admin_role)
    admin_user.depts.add(root_dept)
    
    session.add(admin_user)
    
    logger.info("用户数据初始化完成")


if __name__ == "__main__":
    # 直接运行此脚本进行管理员数据初始化
    asyncio.run(init_admin_data())
