#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : dept.py
# @IDE            : PyCharm
# @desc           : 部门管理服务

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import joinedload
from apps.admin.depts import AdminService
from models.admin.auth.dept import AdminDept
from models.admin.auth.user import AdminUser
from models.admin.auth.m2m import admin_auth_user_depts
from apps.admin.schemas.dept import (
    DeptCreate, DeptUpdate, DeptOut, DeptTreeOut, 
    DeptOptionOut, DeptListOut, DeptUserTreeOut, DeptUserOut, DeptStatsOut
)
from apps.admin.params.dept import DeptParams
from utils.response import SuccessResponse, ErrorResponse
from core.logging import logger
from typing import List, Optional, Dict, Any


class DeptService(AdminService):
    """
    部门管理服务类
    """
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.dept_dal = self.get_dal(AdminDept, DeptOut)
        self.user_dal = self.get_dal(AdminUser)

    async def get_depts(self, params: DeptParams, paging) -> dict:
        """
        获取部门列表
        """
        try:
            filters = {}
            
            # 构建查询条件
            if params.name:
                filters["name"] = ("like", params.name)
            if params.dept_key:
                filters["dept_key"] = ("like", params.dept_key)
            if params.parent_id is not None:
                filters["parent_id"] = params.parent_id
            if params.disabled is not None:
                filters["disabled"] = params.disabled
            if params.owner:
                filters["owner"] = ("like", params.owner)
            if params.create_datetime_start:
                filters["create_datetime"] = (">=", params.create_datetime_start)
            if params.create_datetime_end:
                filters["create_datetime"] = ("<=", params.create_datetime_end)

            # 获取数据
            datas, count = await self.dept_dal.get_datas(
                page=paging.page,
                limit=paging.limit,
                v_return_count=True,
                v_order=paging.v_order,
                v_order_field=paging.v_order_field,
                v_schema=DeptListOut,
                **filters
            )

            return SuccessResponse(data=datas, count=count)

        except Exception as e:
            logger.error(f"获取部门列表失败: {e}")
            return ErrorResponse(msg=f"获取部门列表失败: {str(e)}")

    async def create_dept(self, data: DeptCreate) -> dict:
        """
        创建部门
        """
        try:
            # 检查部门名称是否已存在
            existing_dept = await self.dept_dal.get_data(
                name=data.name,
                v_return_none=True
            )
            if existing_dept:
                return ErrorResponse(msg="部门名称已存在")

            # 检查部门标识是否已存在
            existing_key = await self.dept_dal.get_data(
                dept_key=data.dept_key,
                v_return_none=True
            )
            if existing_key:
                return ErrorResponse(msg="部门标识已存在")

            # 验证父部门是否存在
            if data.parent_id:
                parent_dept = await self.dept_dal.get_data(
                    data.parent_id,
                    v_return_none=True
                )
                if not parent_dept:
                    return ErrorResponse(msg="父部门不存在")

            # 创建部门
            dept_data = data.dict()
            dept = await self.dept_dal.create_data(data=dept_data)

            return SuccessResponse(data=DeptOut.from_orm(dept), msg="创建成功")

        except Exception as e:
            logger.error(f"创建部门失败: {e}")
            return ErrorResponse(msg=f"创建失败: {str(e)}")

    async def update_dept(self, dept_id: int, data: DeptUpdate) -> dict:
        """
        更新部门
        """
        try:
            # 检查部门是否存在
            dept = await self.dept_dal.get_data(dept_id, v_return_none=True)
            if not dept:
                return ErrorResponse(msg="部门不存在")

            # 检查部门名称是否重复（如果要更新名称）
            if data.name and data.name != dept.name:
                existing_dept = await self.dept_dal.get_data(
                    name=data.name,
                    v_return_none=True
                )
                if existing_dept:
                    return ErrorResponse(msg="部门名称已存在")

            # 检查部门标识是否重复（如果要更新标识）
            if data.dept_key and data.dept_key != dept.dept_key:
                existing_key = await self.dept_dal.get_data(
                    dept_key=data.dept_key,
                    v_return_none=True
                )
                if existing_key:
                    return ErrorResponse(msg="部门标识已存在")

            # 验证父部门是否存在（如果要更新父部门）
            if data.parent_id and data.parent_id != dept.parent_id:
                # 不能将部门设置为自己的子部门
                if data.parent_id == dept_id:
                    return ErrorResponse(msg="不能将部门设置为自己的子部门")
                
                # 检查是否会形成循环引用
                if await self._check_circular_reference(dept_id, data.parent_id):
                    return ErrorResponse(msg="不能形成循环引用")
                
                parent_dept = await self.dept_dal.get_data(
                    data.parent_id,
                    v_return_none=True
                )
                if not parent_dept:
                    return ErrorResponse(msg="父部门不存在")

            # 更新部门
            update_data = data.dict(exclude_unset=True)
            await self.dept_dal.put_data(dept_id, update_data)

            # 获取更新后的部门信息
            updated_dept = await self.dept_dal.get_data(dept_id)
            
            return SuccessResponse(data=DeptOut.from_orm(updated_dept), msg="更新成功")

        except Exception as e:
            logger.error(f"更新部门失败: {e}")
            return ErrorResponse(msg=f"更新失败: {str(e)}")

    async def delete_depts(self, dept_ids: List[int]) -> dict:
        """
        批量删除部门
        """
        try:
            # 检查是否有子部门
            for dept_id in dept_ids:
                children = await self.dept_dal.get_datas(parent_id=dept_id)
                if children:
                    return ErrorResponse(msg=f"部门ID {dept_id} 存在子部门，无法删除")

                # 检查是否有用户
                users = await self.user_dal.get_datas(dept_id=dept_id)
                if users:
                    return ErrorResponse(msg=f"部门ID {dept_id} 存在用户，无法删除")

            # 软删除部门
            await self.dept_dal.delete_datas(dept_ids, v_soft=True)

            return SuccessResponse(msg="删除成功")

        except Exception as e:
            logger.error(f"删除部门失败: {e}")
            return ErrorResponse(msg=f"删除失败: {str(e)}")

    async def get_dept(self, dept_id: int) -> dict:
        """
        获取部门详情
        """
        try:
            dept = await self.dept_dal.get_data(dept_id, v_return_none=True)
            if not dept:
                return ErrorResponse(msg="部门不存在")

            return SuccessResponse(data=DeptOut.from_orm(dept))

        except Exception as e:
            logger.error(f"获取部门详情失败: {e}")
            return ErrorResponse(msg=f"获取部门详情失败: {str(e)}")

    async def get_dept_tree(self, include_disabled: bool = False) -> dict:
        """
        获取部门树
        """
        try:
            # 构建查询条件
            filters = {}
            if not include_disabled:
                filters["disabled"] = False

            # 获取所有部门
            depts = await self.dept_dal.get_datas(
                v_schema=DeptTreeOut,
                v_order="asc",
                v_order_field="order",
                **filters
            )

            # 构建树结构
            tree = self._build_dept_tree(depts)

            return SuccessResponse(data=tree)

        except Exception as e:
            logger.error(f"获取部门树失败: {e}")
            return ErrorResponse(msg=f"获取部门树失败: {str(e)}")

    async def get_dept_options(self, exclude_id: Optional[int] = None) -> dict:
        """
        获取部门选项（用于父部门选择）
        """
        try:
            filters = {"disabled": False}
            if exclude_id:
                filters["id"] = ("!=", exclude_id)

            depts = await self.dept_dal.get_datas(
                v_schema=DeptOptionOut,
                v_order="asc",
                v_order_field="order",
                **filters
            )

            # 构建树结构选项
            options = self._build_dept_tree(depts)

            return SuccessResponse(data=options)

        except Exception as e:
            logger.error(f"获取部门选项失败: {e}")
            return ErrorResponse(msg=f"获取部门选项失败: {str(e)}")

    async def get_dept_user_tree(self) -> dict:
        """
        获取部门用户树
        """
        try:
            # 获取所有启用的部门
            depts = await self.dept_dal.get_datas(
                disabled=False,
                v_schema=DeptUserTreeOut,
                v_order="asc",
                v_order_field="order"
            )

            # 获取所有启用的用户
            users = await self.user_dal.get_datas(
                is_active=True,
                v_schema=DeptUserOut
            )

            # 构建部门用户树
            tree = self._build_dept_user_tree(depts, users)

            return SuccessResponse(data=tree)

        except Exception as e:
            logger.error(f"获取部门用户树失败: {e}")
            return ErrorResponse(msg=f"获取部门用户树失败: {str(e)}")

    async def get_dept_stats(self, dept_id: Optional[int] = None) -> dict:
        """
        获取部门统计信息
        """
        try:
            if dept_id:
                # 获取指定部门的统计
                stats = await self._get_single_dept_stats(dept_id)
                return SuccessResponse(data=stats)
            else:
                # 获取所有部门的统计
                depts = await self.dept_dal.get_datas(disabled=False)
                stats_list = []
                
                for dept in depts:
                    stats = await self._get_single_dept_stats(dept.id)
                    stats_list.append(stats)
                
                return SuccessResponse(data=stats_list)

        except Exception as e:
            logger.error(f"获取部门统计失败: {e}")
            return ErrorResponse(msg=f"获取部门统计失败: {str(e)}")

    def _build_dept_tree(self, depts: List) -> List:
        """
        构建部门树结构
        """
        dept_dict = {dept.id: dept for dept in depts}
        tree = []

        for dept in depts:
            if dept.parent_id is None:
                # 顶级部门
                dept.children = self._get_dept_children(dept.id, dept_dict)
                tree.append(dept)

        return tree

    def _get_dept_children(self, parent_id: int, dept_dict: Dict) -> List:
        """
        递归获取子部门
        """
        children = []
        for dept in dept_dict.values():
            if dept.parent_id == parent_id:
                dept.children = self._get_dept_children(dept.id, dept_dict)
                children.append(dept)
        
        # 按order排序
        children.sort(key=lambda x: x.order or 0)
        return children

    def _build_dept_user_tree(self, depts: List, users: List) -> List:
        """
        构建部门用户树结构
        """
        dept_dict = {dept.id: dept for dept in depts}
        
        # 为每个部门添加用户
        for dept in depts:
            dept.children = []
            # 添加该部门的用户
            dept_users = [user for user in users if hasattr(user, 'dept_id') and user.dept_id == dept.id]
            dept.children.extend(dept_users)

        # 构建树结构
        tree = []
        for dept in depts:
            if dept.parent_id is None:
                # 顶级部门
                dept.children.extend(self._get_dept_children(dept.id, dept_dict))
                tree.append(dept)

        return tree

    async def _get_single_dept_stats(self, dept_id: int) -> DeptStatsOut:
        """
        获取单个部门的统计信息
        """
        dept = await self.dept_dal.get_data(dept_id)
        
        # 统计用户数量
        user_count = await self.user_dal.get_count(dept_id=dept_id)
        active_user_count = await self.user_dal.get_count(dept_id=dept_id, is_active=True)
        
        # 统计子部门数量
        sub_dept_count = await self.dept_dal.get_count(parent_id=dept_id)
        
        return DeptStatsOut(
            dept_id=dept_id,
            dept_name=dept.name,
            user_count=user_count,
            active_user_count=active_user_count,
            sub_dept_count=sub_dept_count
        )

    async def _check_circular_reference(self, dept_id: int, parent_id: int) -> bool:
        """
        检查是否会形成循环引用
        """
        current_id = parent_id
        while current_id:
            if current_id == dept_id:
                return True
            
            parent_dept = await self.dept_dal.get_data(current_id, v_return_none=True)
            if not parent_dept:
                break
            
            current_id = parent_dept.parent_id
        
        return False
