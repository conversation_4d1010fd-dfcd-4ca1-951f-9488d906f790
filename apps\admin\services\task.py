# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : task.py
# @IDE            : PyCharm
# @desc           : 任务服务层

import json
import datetime
from typing import Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.depts import AdminDalBase
from apps.admin.schemas.task import TaskCreate, TaskUpdate, TaskOut, TaskListOut, TaskRecordOut
from models.admin.system.task import AdminTask, AdminTaskRecord
from core.logging import logger
from utils.response import SuccessResponse, ErrorResponse


class TaskService:
    """
    任务管理服务
    """
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.task_dal = AdminDalBase(db, AdminTask, TaskOut)
        self.record_dal = AdminDalBase(db, AdminTaskRecord, TaskRecordOut)

    async def get_tasks(self, params, paging) -> dict:
        """
        获取任务列表
        """
        filters = {}
        if params.name:
            filters["name"] = ("like", params.name)
        if params.job_class:
            filters["job_class"] = ("like", params.job_class)
        if params.exec_strategy:
            filters["exec_strategy"] = params.exec_strategy
        if params.group:
            filters["group"] = params.group
        if params.is_active is not None:
            filters["is_active"] = params.is_active

        datas, count = await self.task_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_order_field=paging.v_order_field,
            v_schema=TaskListOut,
            **filters
        )

        return SuccessResponse(data=datas, count=count)

    async def create_task(self, data: TaskCreate) -> dict:
        """
        创建任务
        """
        try:
            # 检查任务名称是否已存在
            existing_task = await self.task_dal.get_data(
                name=data.name,
                v_return_none=True
            )
            if existing_task:
                return ErrorResponse(msg="任务名称已存在")

            # 创建任务记录
            task_data = data.dict()
            task = await self.task_dal.create_data(data=task_data)

            # 如果任务启用，则添加到调度器
            if data.is_active:
                await self._add_task_to_scheduler(task)

            return SuccessResponse(data=TaskOut.from_orm(task), msg="创建成功")

        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return ErrorResponse(msg=f"创建失败: {str(e)}")

    async def update_task(self, task_id: int, data: TaskUpdate) -> dict:
        """
        更新任务
        """
        try:
            # 获取原任务
            task = await self.task_dal.get_data(task_id)
            if not task:
                return ErrorResponse(msg="任务不存在")

            # 如果任务正在运行，先从调度器中移除
            await self._remove_task_from_scheduler(str(task_id))

            # 更新任务
            update_data = data.dict(exclude_unset=True)
            await self.task_dal.put_data(task_id, update_data)

            # 重新获取更新后的任务
            updated_task = await self.task_dal.get_data(task_id)

            # 如果任务启用，重新添加到调度器
            if updated_task.is_active:
                await self._add_task_to_scheduler(updated_task)

            return SuccessResponse(data=TaskOut.from_orm(updated_task), msg="更新成功")

        except Exception as e:
            logger.error(f"更新任务失败: {e}")
            return ErrorResponse(msg=f"更新失败: {str(e)}")

    async def delete_task(self, task_id: int) -> dict:
        """
        删除任务
        """
        try:
            # 获取任务
            task = await self.task_dal.get_data(task_id)
            if not task:
                return ErrorResponse(msg="任务不存在")

            # 从调度器中移除
            await self._remove_task_from_scheduler(str(task_id))

            # 删除任务
            await self.task_dal.delete_datas([task_id])

            return SuccessResponse(msg="删除成功")

        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            return ErrorResponse(msg=f"删除失败: {str(e)}")

    async def get_task(self, task_id: int) -> dict:
        """
        获取单个任务详情
        """
        task = await self.task_dal.get_data(task_id, v_return_none=True)
        if not task:
            return ErrorResponse(msg="任务不存在")

        return SuccessResponse(data=TaskOut.from_orm(task))

    async def execute_task(self, task_id: int, args: str = None, kwargs: str = None) -> dict:
        """
        立即执行任务
        """
        try:
            # 获取任务
            task = await self.task_dal.get_data(task_id)
            if not task:
                return ErrorResponse(msg="任务不存在")

            # 解析参数
            task_args = json.loads(args) if args else []
            task_kwargs = json.loads(kwargs) if kwargs else {}

            # 立即执行任务
            await self._execute_task_immediately(task, task_args, task_kwargs)

            return SuccessResponse(msg="任务执行成功")

        except Exception as e:
            logger.error(f"执行任务失败: {e}")
            return ErrorResponse(msg=f"执行失败: {str(e)}")

    async def toggle_task_status(self, task_id: int) -> dict:
        """
        切换任务启用状态
        """
        try:
            # 获取任务
            task = await self.task_dal.get_data(task_id)
            if not task:
                return ErrorResponse(msg="任务不存在")

            # 切换状态
            new_status = not task.is_active
            await self.task_dal.put_data(task_id, {"is_active": new_status})

            # 更新调度器
            if new_status:
                # 启用任务，添加到调度器
                updated_task = await self.task_dal.get_data(task_id)
                await self._add_task_to_scheduler(updated_task)
            else:
                # 禁用任务，从调度器移除
                await self._remove_task_from_scheduler(str(task_id))

            status_text = "启用" if new_status else "禁用"
            return SuccessResponse(msg=f"任务{status_text}成功")

        except Exception as e:
            logger.error(f"切换任务状态失败: {e}")
            return ErrorResponse(msg=f"操作失败: {str(e)}")

    async def get_task_status(self, task_id: int) -> dict:
        """
        获取任务运行状态
        """
        try:
            # 获取任务
            task = await self.task_dal.get_data(task_id)
            if not task:
                return ErrorResponse(msg="任务不存在")

            # 获取调度器中的任务状态
            status = await self._get_scheduler_task_status(str(task_id))

            return SuccessResponse(data=status)

        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return ErrorResponse(msg=f"获取状态失败: {str(e)}")

    async def get_task_records(self, params, paging) -> dict:
        """
        获取任务执行记录
        """
        filters = {}
        if params.job_id:
            filters["job_id"] = params.job_id
        if params.name:
            filters["name"] = ("like", params.name)
        if params.group:
            filters["group"] = params.group
        if params.exec_strategy:
            filters["exec_strategy"] = params.exec_strategy
        if params.start_time_start:
            filters["start_time"] = (">=", params.start_time_start)
        if params.start_time_end:
            filters["start_time"] = ("<=", params.start_time_end)
        if params.has_exception is not None:
            if params.has_exception:
                filters["exception"] = ("!=", None)
            else:
                filters["exception"] = None

        datas, count = await self.record_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_order_field=paging.v_order_field,
            **filters
        )

        return SuccessResponse(data=datas, count=count)

    async def get_scheduler_status(self) -> dict:
        """
        获取调度器状态
        """
        try:
            # 这里应该调用调度器获取状态
            # 暂时返回模拟数据
            status = {
                "is_running": False,
                "job_count": 0,
                "jobs": []
            }
            
            return SuccessResponse(data=status, msg="调度器状态获取成功")
        except Exception as e:
            logger.error(f"获取调度器状态失败: {e}")
            return ErrorResponse(msg=f"获取状态失败: {str(e)}")

    async def _add_task_to_scheduler(self, task: AdminTask) -> None:
        """
        将任务添加到调度器
        """
        try:
            # 这里应该调用调度器添加任务
            # 暂时只记录日志
            logger.info(f"任务 {task.name} 已添加到调度器（模拟）")
        except Exception as e:
            logger.error(f"添加任务到调度器失败: {e}")
            raise

    async def _remove_task_from_scheduler(self, task_id: str) -> None:
        """
        从调度器移除任务
        """
        try:
            # 这里应该调用调度器移除任务
            # 暂时只记录日志
            logger.info(f"任务 {task_id} 已从调度器移除（模拟）")
        except Exception as e:
            logger.error(f"从调度器移除任务失败: {e}")

    async def _execute_task_immediately(self, task: AdminTask, args: list, kwargs: dict) -> None:
        """
        立即执行任务
        """
        try:
            # 这里应该调用调度器立即执行任务
            # 暂时只记录日志
            logger.info(f"立即执行任务 {task.name}（模拟）")
        except Exception as e:
            logger.error(f"立即执行任务失败: {e}")
            raise

    async def _get_scheduler_task_status(self, task_id: str) -> dict:
        """
        获取调度器中的任务状态
        """
        try:
            # 这里应该调用调度器获取任务状态
            # 暂时返回模拟数据
            return {
                "job_id": task_id,
                "is_running": False,
                "next_run_time": None,
                "last_run_time": None
            }
        except Exception as e:
            logger.error(f"获取调度器任务状态失败: {e}")
            return {
                "job_id": task_id,
                "is_running": False,
                "next_run_time": None,
                "last_run_time": None
            }
