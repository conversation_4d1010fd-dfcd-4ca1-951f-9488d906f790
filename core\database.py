from typing import AsyncGenerator
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker, AsyncAttrs
from sqlalchemy.orm import DeclarativeBase, declared_attr
from config.setting import SQLALCHEMY_DATABASE_URL, REDIS_DB_ENABLE, MONGO_DB_ENABLE
from fastapi import Request
from core.exception import CustomException
from motor.motor_asyncio import AsyncIOMotorDatabase


# 创建数据库连接
async_engine = create_async_engine(
    SQLALCHEMY_DATABASE_URL,
    echo=False,
    echo_pool=False,
    pool_pre_ping=True,
    pool_recycle=3600,
    pool_size=10,  # 增加连接池大小以支持更多并发连接
    max_overflow=20,  # 增加允许的最大溢出连接数
    pool_timeout=30,  # 设置获取连接的超时时间为30秒
    connect_args={}
)

# 创建数据库会话
session_factory = async_sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=async_engine,
    expire_on_commit=True,
    class_=AsyncSession
)


class Base(AsyncAttrs, DeclarativeBase):
    """创建基本映射类，models中的类都通过BaseModel继承这个类"""

    @declared_attr.directive
    def __tablename__(cls) -> str:
        """将表名改为小写，如果有自定义表名就取自定义，没有就取小写类名"""
        table_name = cls.__tablename__
        if not table_name:
            model_name = cls.__name__
            ls = []
            for index, char in enumerate(model_name):
                if char.isupper() and index != 0:
                    ls.append("_")
                ls.append(char)
            table_name = "".join(ls).lower()
        return table_name


async def db_getter() -> AsyncGenerator[AsyncSession, None]:
    """获取主数据库会话，数据库依赖项，它将在单个请求中使用，然后在请求完成后将其关闭。"""
    async with session_factory() as session:
        # 创建一个新的事务，半自动 commit
        async with session.begin():
            yield session


def redis_getter(request: Request) -> Redis:
    """获取 redis 数据库对象，全局挂载，使用一个数据库对象"""
    if not REDIS_DB_ENABLE:
        raise CustomException("请先配置Redis数据库链接并启用！", desc="请启用 config/setting.py: REDIS_DB_ENABLE")
    return request.app.state.redis


def mongo_getter(request: Request) -> AsyncIOMotorDatabase:
    """获取 mongo 数据库对象，全局挂载，使用一个数据库对象"""
    if not MONGO_DB_ENABLE:
        raise CustomException(
            msg="请先开启 MongoDB 数据库连接！",
            desc="请启用 config/setting.py: MONGO_DB_ENABLE"
        )
    return request.app.state.mongo
