# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2021/10/19 15:47
# @File           : main.py
# @IDE            : PyCharm
# @desc           : 主程序入口

"""
FastApi 更新文档：https://github.com/tiangolo/fastapi/releases
FastApi Github：https://github.com/tiangolo/fastapi
Typer 官方文档：https://typer.tiangolo.com/
"""

from fastapi import FastAPI
import uvicorn
from starlette.middleware.cors import CORSMiddleware
from config import setting
from config.setting import VERSION, CORS_ORIGIN_ENABLE, ALLOW_ORIGINS, ALLOW_CREDENTIALS, ALLOW_METHODS, ALLOW_HEADERS, STATIC_ENABLE, STATIC_URL, STATIC_ROOT, MIDDLEWARES
from starlette.staticfiles import StaticFiles  # 依赖安装：pip install aiofiles
from core.docs import custom_api_docs
from core.exception import register_exception
import typer
from core.event import lifespan
from core.scheduler import init_task_manager, shutdown_task_manager
from utils.tools import import_modules

# 导入路由配置
from apps.router import urlpatterns

shell_app = typer.Typer()


def create_app():
    """
    启动项目

    docs_url：配置交互文档的路由地址，如果禁用则为None，默认为 /docs
    redoc_url： 配置 Redoc 文档的路由地址，如果禁用则为None，默认为 /redoc
    openapi_url：配置接口文件json数据文件路由地址，如果禁用则为None，默认为/openapi.json
    """
    app = FastAPI(
        title="Kinit",
        description="本项目基于Fastapi与Vue3+Typescript+Vite4+element-plus的基础项目 前端基于vue-element-plus-admin框架开发",
        version=VERSION,
        lifespan=lifespan,
        docs_url=None,
        redoc_url=None
    )
    import_modules(MIDDLEWARES, "中间件", app=app)
    # 全局异常捕捉处理
    register_exception(app)
    # 跨域解决
    if CORS_ORIGIN_ENABLE:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=ALLOW_ORIGINS,
            allow_credentials=ALLOW_CREDENTIALS,
            allow_methods=ALLOW_METHODS,
            allow_headers=ALLOW_HEADERS
        )
    # 挂在静态目录
    if STATIC_ENABLE:
        app.mount(STATIC_URL, app=StaticFiles(directory=STATIC_ROOT))
    
    # 引入应用中的路由 - 保持原有的接口路径不变
    for url in urlpatterns:
        app.include_router(url["ApiRouter"], prefix=url["prefix"], tags=url["tags"])
    
    # 配置接口文档静态资源
    custom_api_docs(app)
    return app


@shell_app.command()
def run(
        host: str = typer.Option(default='0.0.0.0', help='监听主机IP，默认开放给本网络所有主机'),
        port: int = typer.Option(default=9000, help='监听端口')
):
    """
    启动项目

    factory: 在使用 uvicorn.run() 启动 ASGI 应用程序时，可以通过设置 factory 参数来指定应用程序工厂。
    应用程序工厂是一个返回 ASGI 应用程序实例的可调用对象，它可以在启动时动态创建应用程序实例。
    """
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)


@shell_app.command()
def init(env: str = "dev"):
    """
    初始化数据

    在执行前一定要确认要操作的环境与config/setting.DEBUG 设置的环境是一致的，
    不然会导致创建表和生成数据不在一个数据库中！！！！！！！！！！！！！！！！！！！！！！

    比如要初始化开发环境，那么env参数应该为 dev，并且 config/setting.DEBUG 应该 = True
    比如要初始化生产环境，那么env参数应该为 pro，并且 config/setting.DEBUG 应该 = False

    :param env: 数据库环境
    """
    print(f"开始初始化 {env} 环境数据...")

    # 验证环境配置
    from config.setting import DEBUG
    if env == "dev" and not DEBUG:
        print("错误：要初始化开发环境，请设置 config/setting.DEBUG = True")
        return
    elif env == "pro" and DEBUG:
        print("错误：要初始化生产环境，请设置 config/setting.DEBUG = False")
        return

    # 导入并运行初始化脚本
    try:
        from scripts.init_data import run_init
        success = run_init(env)

        if success:
            print(f"✅ {env} 环境数据初始化成功！")
            print("📋 默认管理员账号信息：")
            print("   📱 手机号: 13800138000")
            print("   🔑 密码: admin123")
            print("   🌐 请访问系统进行登录测试")
        else:
            print(f"❌ {env} 环境数据初始化失败！")
            print("请检查日志文件获取详细错误信息")

    except Exception as e:
        print(f"❌ 初始化过程中发生错误: {str(e)}")
        print("请检查数据库连接配置和日志文件")


@shell_app.command()
def migrate(env: str = "pro"):
    """
    将模型迁移到数据库，更新数据库表结构

    :param env: 数据库环境
    """
    print(f"开始更新 {env} 环境数据库表结构...")

    # 验证环境配置
    from config.setting import DEBUG
    if env == "dev" and not DEBUG:
        print("错误：要操作开发环境，请设置 config/setting.DEBUG = True")
        return
    elif env == "pro" and DEBUG:
        print("错误：要操作生产环境，请设置 config/setting.DEBUG = False")
        return

    # 导入并运行数据库迁移
    try:
        import asyncio
        from scripts.database import init_database_tables, check_database_connection

        async def run_migrate():
            # 检查数据库连接
            if not await check_database_connection():
                print("❌ 数据库连接失败")
                return False

            # 执行表结构更新
            if await init_database_tables():
                print("✅ 数据库表结构更新成功！")
                return True
            else:
                print("❌ 数据库表结构更新失败！")
                return False

        success = asyncio.run(run_migrate())
        if not success:
            print("请检查日志文件获取详细错误信息")

    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {str(e)}")
        print("请检查数据库连接配置和日志文件")


if __name__ == '__main__':
    shell_app()
