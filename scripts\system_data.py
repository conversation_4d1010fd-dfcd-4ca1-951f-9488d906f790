#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : system_data.py
# @IDE            : PyCharm
# @desc           : 系统配置数据初始化

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from core.database import session_factory
from core.logging import logger

# 导入系统模型
try:
    from models.admin.system.dict import AdminDictType, AdminDictDetails
    from models.admin.system.settings import AdminSystemSettings, AdminSystemSettingsTab
    SYSTEM_MODELS_AVAILABLE = True
except ImportError:
    SYSTEM_MODELS_AVAILABLE = False
    logger.warning("系统模型不可用，跳过系统数据初始化")


async def init_system_data():
    """
    初始化系统配置数据
    """
    if not SYSTEM_MODELS_AVAILABLE:
        logger.info("系统模型不可用，跳过系统数据初始化")
        return True
        
    try:
        logger.info("开始初始化系统配置数据...")
        
        async with session_factory() as session:
            # 初始化字典数据
            await init_dict_data(session)
            
            # 初始化系统设置数据
            await init_settings_data(session)
            
            # 提交事务
            await session.commit()
            
        logger.info("系统配置数据初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"系统配置数据初始化失败: {str(e)}")
        return False


async def init_dict_data(session: AsyncSession):
    """
    初始化字典数据
    """
    if not SYSTEM_MODELS_AVAILABLE:
        return
        
    logger.info("初始化字典数据...")
    
    # 检查是否已存在字典数据
    result = await session.execute(select(AdminDictType))
    if result.first():
        logger.info("字典数据已存在，跳过初始化")
        return
    
    # 创建字典类型
    dict_types = [
        {
            "dict_name": "用户性别",
            "dict_type": "user_gender",
            "disabled": False,
            "remark": "用户性别字典"
        },
        {
            "dict_name": "菜单类型",
            "dict_type": "menu_type", 
            "disabled": False,
            "remark": "菜单类型字典"
        },
        {
            "dict_name": "数据权限范围",
            "dict_type": "data_range",
            "disabled": False,
            "remark": "角色数据权限范围"
        }
    ]
    
    for dict_type_data in dict_types:
        dict_type = AdminDictType(**dict_type_data)
        session.add(dict_type)
        await session.flush()  # 获取ID
        
        # 为每个字典类型创建详情数据
        if dict_type_data["dict_type"] == "user_gender":
            details = [
                {"label": "男", "value": "1", "order": 1, "is_default": True},
                {"label": "女", "value": "2", "order": 2, "is_default": False},
                {"label": "未知", "value": "0", "order": 3, "is_default": False}
            ]
        elif dict_type_data["dict_type"] == "menu_type":
            details = [
                {"label": "目录", "value": "directory", "order": 1, "is_default": False},
                {"label": "菜单", "value": "menu", "order": 2, "is_default": True},
                {"label": "按钮", "value": "button", "order": 3, "is_default": False}
            ]
        elif dict_type_data["dict_type"] == "data_range":
            details = [
                {"label": "全部数据权限", "value": "1", "order": 1, "is_default": False},
                {"label": "自定数据权限", "value": "2", "order": 2, "is_default": False},
                {"label": "本部门数据权限", "value": "3", "order": 3, "is_default": False},
                {"label": "本部门及以下数据权限", "value": "4", "order": 4, "is_default": True},
                {"label": "仅本人数据权限", "value": "5", "order": 5, "is_default": False}
            ]
        else:
            details = []
        
        for detail_data in details:
            detail = AdminDictDetails(
                **detail_data,
                dict_type_id=dict_type.id,
                disabled=False
            )
            session.add(detail)
    
    logger.info("字典数据初始化完成")


async def init_settings_data(session: AsyncSession):
    """
    初始化系统设置数据
    """
    if not SYSTEM_MODELS_AVAILABLE:
        return
        
    logger.info("初始化系统设置数据...")
    
    # 检查是否已存在设置数据
    result = await session.execute(select(AdminSystemSettingsTab))
    if result.first():
        logger.info("系统设置数据已存在，跳过初始化")
        return
    
    # 创建设置标签
    settings_tabs = [
        {
            "title": "基础配置",
            "classify": "base",
            "tab_label": "基础配置",
            "tab_name": "base_config",
            "hidden": False,
            "disabled": False
        },
        {
            "title": "系统配置",
            "classify": "system",
            "tab_label": "系统配置", 
            "tab_name": "system_config",
            "hidden": False,
            "disabled": False
        }
    ]
    
    for tab_data in settings_tabs:
        tab = AdminSystemSettingsTab(**tab_data)
        session.add(tab)
        await session.flush()  # 获取ID
        
        # 为每个标签创建配置项
        if tab_data["tab_name"] == "base_config":
            settings = [
                {
                    "config_label": "网站标题",
                    "config_key": "web_title",
                    "config_value": "Kinit管理系统",
                    "remark": "网站标题配置"
                },
                {
                    "config_label": "网站描述",
                    "config_key": "web_desc", 
                    "config_value": "基于FastAPI的管理系统",
                    "remark": "网站描述配置"
                },
                {
                    "config_label": "网站Logo",
                    "config_key": "web_logo",
                    "config_value": "",
                    "remark": "网站Logo配置"
                }
            ]
        elif tab_data["tab_name"] == "system_config":
            settings = [
                {
                    "config_label": "默认密码",
                    "config_key": "default_password",
                    "config_value": "123456",
                    "remark": "用户默认密码"
                },
                {
                    "config_label": "登录失败最大次数",
                    "config_key": "login_max_error",
                    "config_value": "5",
                    "remark": "登录失败最大次数限制"
                }
            ]
        else:
            settings = []
        
        for setting_data in settings:
            setting = AdminSystemSettings(
                **setting_data,
                tab_id=tab.id,
                disabled=False
            )
            session.add(setting)
    
    logger.info("系统设置数据初始化完成")


if __name__ == "__main__":
    # 直接运行此脚本进行系统数据初始化
    asyncio.run(init_system_data())
