#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : test_init.py
# @IDE            : PyCharm
# @desc           : 测试数据库初始化功能

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from core.database import session_factory
from core.logging import logger

# 导入模型
from models.admin.auth.user import AdminUser
from models.admin.auth.role import AdminRole
from models.admin.auth.dept import AdminDept
from models.admin.auth.menu import AdminMenu


async def test_database_init():
    """
    测试数据库初始化结果
    """
    try:
        logger.info("开始测试数据库初始化结果...")
        
        async with session_factory() as session:
            # 测试部门数据
            await test_departments(session)
            
            # 测试菜单数据
            await test_menus(session)
            
            # 测试角色数据
            await test_roles(session)
            
            # 测试用户数据
            await test_users(session)
            
        logger.info("数据库初始化测试完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化测试失败: {str(e)}")
        return False


async def test_departments(session: AsyncSession):
    """
    测试部门数据
    """
    logger.info("测试部门数据...")
    
    # 统计部门数量
    result = await session.execute(select(func.count(AdminDept.id)))
    dept_count = result.scalar()
    logger.info(f"部门总数: {dept_count}")
    
    # 查询根部门
    result = await session.execute(
        select(AdminDept).where(AdminDept.dept_key == "root")
    )
    root_dept = result.scalar_one_or_none()
    
    if root_dept:
        logger.info(f"根部门: {root_dept.name} (ID: {root_dept.id})")
        
        # 查询子部门
        result = await session.execute(
            select(AdminDept).where(AdminDept.parent_id == root_dept.id)
        )
        sub_depts = result.scalars().all()
        
        for dept in sub_depts:
            logger.info(f"  子部门: {dept.name} ({dept.dept_key})")
    else:
        logger.warning("未找到根部门")


async def test_menus(session: AsyncSession):
    """
    测试菜单数据
    """
    logger.info("测试菜单数据...")
    
    # 统计菜单数量
    result = await session.execute(select(func.count(AdminMenu.id)))
    menu_count = result.scalar()
    logger.info(f"菜单总数: {menu_count}")
    
    # 查询主菜单
    result = await session.execute(
        select(AdminMenu).where(AdminMenu.parent_id.is_(None))
    )
    main_menus = result.scalars().all()
    
    for menu in main_menus:
        logger.info(f"主菜单: {menu.title} ({menu.name})")
        
        # 查询子菜单
        result = await session.execute(
            select(AdminMenu).where(AdminMenu.parent_id == menu.id)
        )
        sub_menus = result.scalars().all()
        
        for sub_menu in sub_menus:
            logger.info(f"  子菜单: {sub_menu.title} ({sub_menu.name})")


async def test_roles(session: AsyncSession):
    """
    测试角色数据
    """
    logger.info("测试角色数据...")
    
    # 统计角色数量
    result = await session.execute(select(func.count(AdminRole.id)))
    role_count = result.scalar()
    logger.info(f"角色总数: {role_count}")
    
    # 查询所有角色
    result = await session.execute(select(AdminRole))
    roles = result.scalars().all()
    
    for role in roles:
        logger.info(f"角色: {role.name} ({role.role_key}) - 超级管理员: {role.is_admin}")


async def test_users(session: AsyncSession):
    """
    测试用户数据
    """
    logger.info("测试用户数据...")
    
    # 统计用户数量
    result = await session.execute(select(func.count(AdminUser.id)))
    user_count = result.scalar()
    logger.info(f"用户总数: {user_count}")
    
    # 查询管理员用户
    result = await session.execute(
        select(AdminUser).where(AdminUser.telephone == "13800138000")
    )
    admin_user = result.scalar_one_or_none()
    
    if admin_user:
        logger.info(f"管理员用户: {admin_user.name} ({admin_user.telephone})")
        logger.info(f"  邮箱: {admin_user.email}")
        logger.info(f"  状态: {'激活' if admin_user.is_active else '禁用'}")
        logger.info(f"  角色数量: {len(admin_user.roles)}")
        logger.info(f"  部门数量: {len(admin_user.depts)}")
    else:
        logger.warning("未找到管理员用户")


async def test_login_credentials():
    """
    测试登录凭据
    """
    logger.info("测试登录凭据...")
    
    async with session_factory() as session:
        # 查询管理员用户
        result = await session.execute(
            select(AdminUser).where(AdminUser.telephone == "13800138000")
        )
        admin_user = result.scalar_one_or_none()
        
        if admin_user:
            # 验证密码
            from models.admin.auth.user import pwd_context
            is_valid = pwd_context.verify("admin123", admin_user.password)
            logger.info(f"密码验证: {'通过' if is_valid else '失败'}")
        else:
            logger.error("未找到管理员用户，无法测试登录凭据")


def run_test():
    """
    运行测试
    """
    async def main():
        success = await test_database_init()
        if success:
            await test_login_credentials()
        return success
    
    return asyncio.run(main())


if __name__ == "__main__":
    # 直接运行此脚本进行测试
    success = run_test()
    if success:
        print("✅ 数据库初始化测试通过！")
    else:
        print("❌ 数据库初始化测试失败！")
        exit(1)
