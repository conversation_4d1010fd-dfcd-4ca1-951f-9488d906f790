<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="1">
            <item index="0" class="java.lang.String" itemvalue="pydantic-validation-decorator" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="Stylelint" enabled="true" level="ERROR" enabled_by_default="true" />
  </profile>
</component>