# 数据库初始化使用指南

## 🚀 快速开始

### 1. 完整初始化（推荐）

```bash
# 开发环境初始化（需要设置 config/setting.DEBUG = True）
python main.py init dev

# 生产环境初始化（需要设置 config/setting.DEBUG = False）  
python main.py init pro
```

### 2. 仅更新数据库表结构

```bash
# 开发环境
python main.py migrate dev

# 生产环境
python main.py migrate pro
```

## 📋 初始化内容

### 默认管理员账号
- **手机号**: 13800138000
- **密码**: admin123
- **邮箱**: <EMAIL>

### 基础数据
- **部门**: 总公司、技术部、产品部、运营部
- **角色**: 超级管理员、普通用户
- **菜单**: 系统管理模块（用户、角色、部门、菜单管理）
- **配置**: 系统字典、基础设置

## ⚠️ 注意事项

### 环境配置
1. **开发环境**: 设置 `config/setting.DEBUG = True`
2. **生产环境**: 设置 `config/setting.DEBUG = False`

### 安全提醒
1. 初始化后请立即修改默认管理员密码
2. 生产环境部署前请修改默认配置
3. 重要数据请提前备份

## 🔧 高级用法

### 单独运行脚本

```bash
# 仅初始化数据库表结构
python -m scripts.database

# 仅初始化管理员数据
python -m scripts.admin_data

# 仅初始化系统配置数据
python -m scripts.system_data

# 测试初始化结果
python -m scripts.test_init

# 演示完整流程
python scripts/demo.py full
```

### 演示脚本

```bash
# 完整初始化演示
python scripts/demo.py full

# 单独功能演示
python scripts/demo.py individual

# 显示帮助
python scripts/demo.py help
```

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 检查 `config/env/dev.py` 或 `config/env/pro.py` 中的连接配置

2. **环境配置错误**
   - 确保 `config/setting.DEBUG` 设置与命令参数一致

3. **权限问题**
   - 确保数据库用户有创建表的权限

### 日志查看

详细日志记录在：
- `logs/info_YYYY-MM-DD.log` - 信息日志
- `logs/error_YYYY-MM-DD.log` - 错误日志

## 📚 更多信息

详细文档请查看：`scripts/README.md`
