#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : database.py
# @IDE            : PyCharm
# @desc           : 数据库表结构初始化

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from core.database import async_engine, Base, session_factory
from core.logging import logger


async def init_database_tables():
    """
    初始化数据库表结构
    创建所有定义的数据库表
    """
    try:
        logger.info("开始初始化数据库表结构...")
        
        # 导入所有模型以确保它们被注册到Base.metadata中
        import_all_models()
        
        # 创建所有表
        async with async_engine.begin() as conn:
            # 删除所有表（谨慎使用）
            # await conn.run_sync(Base.metadata.drop_all)
            
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
            
        logger.info("数据库表结构初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库表结构初始化失败: {str(e)}")
        return False


def import_all_models():
    """
    导入所有模型以确保它们被注册到SQLAlchemy的metadata中
    """
    try:
        # 导入基础模型
        from models.base import BaseModel
        
        # 导入管理员认证相关模型
        from models.admin.auth.user import AdminUser
        from models.admin.auth.role import AdminRole
        from models.admin.auth.dept import AdminDept
        from models.admin.auth.menu import AdminMenu
        from models.admin.auth.m2m import (
            admin_auth_user_roles,
            admin_auth_role_menus,
            admin_auth_user_depts,
            admin_auth_role_depts
        )
        
        # 导入记录相关模型
        from models.admin.record.login import AdminLoginRecord
        
        # 导入系统相关模型（如果存在）
        try:
            from models.admin.system.dict import AdminDictType, AdminDictDetails
            from models.admin.system.settings import AdminSystemSettings, AdminSystemSettingsTab
            from models.admin.system.task import AdminTask, AdminTaskRecord
        except ImportError:
            # 如果系统模型不存在，跳过
            pass
            
        logger.info("所有模型导入完成")
        
    except Exception as e:
        logger.error(f"导入模型时出错: {str(e)}")
        raise


async def check_database_connection():
    """
    检查数据库连接是否正常
    """
    try:
        async with session_factory() as session:
            # 执行一个简单的查询来测试连接
            result = await session.execute("SELECT 1")
            await result.fetchone()
            logger.info("数据库连接正常")
            return True
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        return False


async def drop_all_tables():
    """
    删除所有表（危险操作，仅用于开发环境）
    """
    try:
        logger.warning("开始删除所有数据库表...")
        
        # 导入所有模型
        import_all_models()
        
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
            
        logger.warning("所有数据库表已删除")
        return True
        
    except Exception as e:
        logger.error(f"删除数据库表失败: {str(e)}")
        return False


if __name__ == "__main__":
    # 直接运行此脚本进行数据库表初始化
    asyncio.run(init_database_tables())
