# 数据库初始化脚本

本目录包含了项目数据库初始化相关的脚本，用于创建数据库表结构和初始化基础数据。

## 文件说明

### 核心文件

- `__init__.py` - 包初始化文件，导出主要功能
- `database.py` - 数据库表结构初始化
- `init_data.py` - 数据初始化主入口
- `admin_data.py` - 管理员系统数据初始化
- `system_data.py` - 系统配置数据初始化
- `test_init.py` - 初始化结果测试脚本

### 文档文件

- `README.md` - 本说明文档

## 使用方法

### 1. 完整初始化（推荐）

使用main.py中的init命令进行完整初始化：

```bash
# 初始化开发环境（需要设置 config/setting.DEBUG = True）
python main.py init dev

# 初始化生产环境（需要设置 config/setting.DEBUG = False）
python main.py init pro
```

### 2. 仅更新数据库表结构

如果只需要更新数据库表结构，可以使用migrate命令：

```bash
# 更新开发环境数据库表结构
python main.py migrate dev

# 更新生产环境数据库表结构
python main.py migrate pro
```

### 3. 单独运行脚本

也可以直接运行单个脚本：

```bash
# 仅初始化数据库表结构
python -m scripts.database

# 仅初始化管理员数据
python -m scripts.admin_data

# 仅初始化系统配置数据
python -m scripts.system_data

# 完整初始化
python -m scripts.init_data

# 测试初始化结果
python -m scripts.test_init
```

## 初始化内容

### 数据库表结构

- 用户认证相关表（用户、角色、部门、菜单）
- 关联关系表（用户-角色、角色-菜单、用户-部门、角色-部门）
- 系统配置表（字典、设置、任务）
- 记录日志表（登录记录、操作记录）

### 基础数据

#### 部门数据
- 总公司（根部门）
- 技术部
- 产品部
- 运营部

#### 菜单数据
- 系统管理（主菜单）
  - 用户管理
  - 角色管理
  - 部门管理
  - 菜单管理

#### 角色数据
- 超级管理员（拥有所有权限）
- 普通用户

#### 用户数据
- 超级管理员用户
  - 手机号：13800138000
  - 密码：admin123
  - 邮箱：<EMAIL>

#### 系统配置数据
- 字典数据（用户性别、菜单类型、数据权限范围）
- 系统设置（网站标题、描述、默认密码等）

## 注意事项

### 环境配置

在执行初始化前，请确保：

1. **开发环境初始化**：
   - 设置 `config/setting.DEBUG = True`
   - 使用 `init dev` 命令

2. **生产环境初始化**：
   - 设置 `config/setting.DEBUG = False`
   - 使用 `init pro` 命令

### 数据库连接

确保数据库连接配置正确：

- 开发环境：`config/env/dev.py`
- 生产环境：`config/env/pro.py`

### 安全提醒

1. **默认密码**：初始化后请立即修改默认管理员密码
2. **生产环境**：生产环境部署前请修改默认配置
3. **备份**：重要数据请提前备份

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 检查连接配置是否正确
   - 检查数据库用户权限

2. **表已存在错误**
   - 数据库中已有表结构，可以跳过表创建
   - 或者删除现有表后重新初始化

3. **数据已存在**
   - 脚本会自动检查数据是否存在
   - 如果数据已存在会跳过初始化

4. **导入错误**
   - 检查Python路径配置
   - 确保所有依赖包已安装

### 日志查看

初始化过程中的详细日志会记录在：
- `logs/info_YYYY-MM-DD.log` - 信息日志
- `logs/error_YYYY-MM-DD.log` - 错误日志

## 开发说明

### 添加新的初始化数据

1. 在对应的脚本文件中添加初始化逻辑
2. 确保数据的依赖关系正确
3. 添加数据存在性检查，避免重复初始化
4. 更新测试脚本验证新数据

### 扩展功能

如需添加新的初始化功能：

1. 创建新的脚本文件
2. 在 `__init__.py` 中导出新功能
3. 在 `init_data.py` 中调用新功能
4. 更新本文档说明
